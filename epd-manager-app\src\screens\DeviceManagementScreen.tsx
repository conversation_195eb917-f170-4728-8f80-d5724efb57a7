// EPD Manager App - 設備管理頁面

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useDevices } from '../stores/deviceStore';
import { useStores } from '../stores/storeStore';
import { COLORS, SIZES } from '../utils/constants';
import { webSocketService } from '../services/WebSocketService';
import { WebSocketMessage } from '../types';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SIZES.SPACING_MD,
    backgroundColor: COLORS.SURFACE,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.TEXT_DISABLED,
  },
  title: {
    fontSize: SIZES.FONT_SIZE_XL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },

  errorContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: COLORS.ERROR,
    padding: SIZES.SPACING_SM,
    margin: SIZES.SPACING_MD,
    borderRadius: SIZES.BORDER_RADIUS_SM,
  },
  errorText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_SM,
    flex: 1,
  },
  errorDismiss: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_MD,
    fontWeight: 'bold',
    padding: SIZES.SPACING_XS,
  },
  scrollView: {
    flex: 1,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: SIZES.SPACING_MD,
    backgroundColor: COLORS.SURFACE,
    margin: SIZES.SPACING_MD,
    borderRadius: SIZES.BORDER_RADIUS_MD,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: SIZES.FONT_SIZE_XL,
    fontWeight: 'bold',
    color: COLORS.PRIMARY,
  },
  statLabel: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
    marginTop: SIZES.SPACING_XS,
  },
  sectionTitle: {
    fontSize: SIZES.FONT_SIZE_LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginHorizontal: SIZES.SPACING_MD,
    marginBottom: SIZES.SPACING_SM,
  },
  deviceCard: {
    backgroundColor: COLORS.SURFACE,
    margin: SIZES.SPACING_MD,
    marginTop: SIZES.SPACING_SM,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    padding: SIZES.SPACING_MD,
    position: 'relative',
  },
  deviceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SIZES.SPACING_SM,
  },
  deviceInfo: {
    flex: 1,
  },
  deviceMac: {
    fontSize: SIZES.FONT_SIZE_MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    fontFamily: 'monospace',
    marginBottom: SIZES.SPACING_XS,
  },
  deviceDetails: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
  },
  statusBadge: {
    paddingHorizontal: SIZES.SPACING_SM,
    paddingVertical: SIZES.SPACING_XS,
    borderRadius: SIZES.BORDER_RADIUS_SM,
  },
  statusOnline: {
    backgroundColor: COLORS.SUCCESS,
  },
  statusOffline: {
    backgroundColor: COLORS.TEXT_DISABLED,
  },
  statusText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_XS,
    fontWeight: '600',
  },
  deviceActions: {
    flexDirection: 'row',
    gap: SIZES.SPACING_SM,
  },
  actionButton: {
    backgroundColor: COLORS.INFO,
    paddingHorizontal: SIZES.SPACING_MD,
    paddingVertical: SIZES.SPACING_SM,
    borderRadius: SIZES.BORDER_RADIUS_SM,
  },
  actionButtonText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_SM,
    fontWeight: '600',
  },
  deleteButton: {
    backgroundColor: COLORS.ERROR,
  },
  deleteButtonText: {
    color: COLORS.SURFACE,
  },
  customBadge: {
    position: 'absolute',
    top: SIZES.SPACING_SM,
    right: SIZES.SPACING_SM,
    backgroundColor: COLORS.WARNING,
    paddingHorizontal: SIZES.SPACING_SM,
    paddingVertical: SIZES.SPACING_XS,
    borderRadius: SIZES.BORDER_RADIUS_SM,
  },
  customBadgeText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_XS,
    fontWeight: '600',
  },
  emptyContainer: {
    alignItems: 'center',
    padding: SIZES.SPACING_XL,
  },
  emptyText: {
    fontSize: SIZES.FONT_SIZE_LG,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SIZES.SPACING_SM,
  },
  emptySubtext: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_DISABLED,
  },
});

export const DeviceManagementScreen: React.FC = () => {
  const [refreshing, setRefreshing] = useState(false);
  const [deviceStats, setDeviceStats] = useState({
    online: 0,
    offline: 0,
    pendingUpdate: 0,
    updated: 0
  });
  const { selectedStore } = useStores();
  const {
    devices,
    customDevices,
    error,
    fetchDevices,
    removeCustomDevice,
    requestDeviceImage,
    refreshDeviceList,
    syncDevices,
    clearError
  } = useDevices();

  // 計算設備統計信息
  const calculateDeviceStats = (deviceList: any[]) => {
    const online = deviceList.filter(d => d.status === 'online').length;
    const offline = deviceList.filter(d => d.status === 'offline').length;
    const updated = deviceList.filter(d => d.imageUpdateStatus === '已更新').length;
    const pendingUpdate = deviceList.filter(d => d.imageUpdateStatus === '未更新' || !d.imageUpdateStatus).length;

    setDeviceStats({
      online,
      offline,
      pendingUpdate,
      updated
    });
  };

  useEffect(() => {
    // 頁面加載時獲取真實設備數據
    if (selectedStore) {
      loadDevices();
    }
  }, [selectedStore]);

  // 當設備列表變化時重新計算統計信息
  useEffect(() => {
    const allDevices = [...devices, ...customDevices];
    calculateDeviceStats(allDevices);
  }, [devices, customDevices]);

  // 設置WebSocket消息處理器
  useEffect(() => {
    // 處理設備狀態更新消息
    const handleDeviceStatusUpdate = (message: WebSocketMessage) => {
      console.log('收到設備狀態更新:', message);
      // 重新獲取設備列表以更新統計信息
      if (selectedStore) {
        loadDevices();
      }
    };

    // 處理設備圖片更新消息
    const handleImageUpdate = (message: WebSocketMessage) => {
      console.log('收到圖片更新消息:', message);
      // 重新獲取設備列表以更新統計信息
      if (selectedStore) {
        loadDevices();
      }
    };

    // 註冊消息處理器
    webSocketService.addMessageHandler('deviceStatus', handleDeviceStatusUpdate);
    webSocketService.addMessageHandler('imageUpdate', handleImageUpdate);
    webSocketService.addMessageHandler('deviceReport', handleDeviceStatusUpdate);

    // 清理函數
    return () => {
      webSocketService.removeMessageHandler('deviceStatus');
      webSocketService.removeMessageHandler('imageUpdate');
      webSocketService.removeMessageHandler('deviceReport');
    };
  }, [selectedStore]);

  // 定時刷新設備統計信息
  useEffect(() => {
    if (!selectedStore) return;

    // 設置定時器，每30秒刷新一次設備統計
    const statsInterval = setInterval(() => {
      console.log('定時刷新設備統計信息');
      loadDevices();
    }, 30000); // 30秒間隔

    // 清理函數
    return () => {
      clearInterval(statsInterval);
    };
  }, [selectedStore]);

  const loadDevices = async () => {
    if (!selectedStore) return;

    // 參考 test-ws-client-interactive.js 的做法，使用 id 作為 storeId
    const storeId = selectedStore.id;
    if (!storeId) {
      console.error('門店 ID 不存在，請重新選擇門店');
      return;
    }

    try {
      await fetchDevices(storeId);
    } catch (error) {
      console.error('載入設備列表失敗:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);

    if (selectedStore) {
      // 參考 test-ws-client-interactive.js 的做法，使用 id 作為 storeId
      const storeId = selectedStore.id;
      if (storeId) {
        // 先同步設備狀態，然後重新獲取設備列表
        await syncDevices(storeId);
        await loadDevices();
      } else {
        console.error('門店 ID 不存在，無法同步設備');
      }
    } else {
      // 如果沒有選擇門店，使用原有的WebSocket模式
      await refreshDeviceList();
    }

    setRefreshing(false);
  };

  const handleRemoveDevice = (index: number, device: any) => {
    Alert.alert(
      '確認刪除',
      `確定要刪除設備 ${device.macAddress} 嗎？`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '刪除',
          style: 'destructive',
          onPress: () => {
            const success = removeCustomDevice(index);
            if (success) {
              Alert.alert('成功', '設備已刪除');
            }
          }
        }
      ]
    );
  };

  const handleRequestImage = (macAddress: string) => {
    requestDeviceImage(macAddress);
    Alert.alert('提示', `已請求設備 ${macAddress} 的預覽圖像`);
  };

  const allDevices = [...devices, ...customDevices];

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>設備管理</Text>
      </View>

      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity onPress={clearError}>
            <Text style={styles.errorDismiss}>✕</Text>
          </TouchableOpacity>
        </View>
      )}

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[COLORS.PRIMARY]}
          />
        }
      >
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: COLORS.SUCCESS }]}>{deviceStats.online}</Text>
            <Text style={styles.statLabel}>在線設備</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: COLORS.TEXT_DISABLED }]}>{deviceStats.offline}</Text>
            <Text style={styles.statLabel}>離線設備</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: COLORS.WARNING }]}>{deviceStats.pendingUpdate}</Text>
            <Text style={styles.statLabel}>待更新圖片</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: COLORS.INFO }]}>{deviceStats.updated}</Text>
            <Text style={styles.statLabel}>已更新圖片</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>設備列表</Text>

        {allDevices.map((device, index) => {
          const isCustomDevice = index >= devices.length;
          const customIndex = isCustomDevice ? index - devices.length : -1;

          return (
            <View key={device.macAddress} style={styles.deviceCard}>
              <View style={styles.deviceHeader}>
                <View style={styles.deviceInfo}>
                  <Text style={styles.deviceMac}>{device.macAddress}</Text>
                  <Text style={styles.deviceDetails}>
                    {device.data.size} | {device.data.colorType || 'BW'} | 
                    電量: {device.data.battery}% | 
                    信號: {device.data.rssi}dBm
                  </Text>
                </View>
                <View style={[
                  styles.statusBadge,
                  device.status === 'online' ? styles.statusOnline : styles.statusOffline
                ]}>
                  <Text style={styles.statusText}>
                    {device.status === 'online' ? '在線' : '離線'}
                  </Text>
                </View>
              </View>

              <View style={styles.deviceActions}>
                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={() => handleRequestImage(device.macAddress)}
                >
                  <Text style={styles.actionButtonText}>請求圖像</Text>
                </TouchableOpacity>

                {isCustomDevice && (
                  <TouchableOpacity
                    style={[styles.actionButton, styles.deleteButton]}
                    onPress={() => handleRemoveDevice(customIndex, device)}
                  >
                    <Text style={[styles.actionButtonText, styles.deleteButtonText]}>
                      刪除
                    </Text>
                  </TouchableOpacity>
                )}
              </View>

              {isCustomDevice && (
                <View style={styles.customBadge}>
                  <Text style={styles.customBadgeText}>自定義</Text>
                </View>
              )}
            </View>
          );
        })}

        {allDevices.length === 0 && (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>暫無設備</Text>
            <Text style={styles.emptySubtext}>請等待設備連接或下拉刷新</Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};
